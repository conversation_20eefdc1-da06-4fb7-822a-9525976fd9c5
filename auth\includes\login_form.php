<?php
/**
 * User Login Form Component
 * Contains the login form with validation and error handling
 */
?>

<div class="container-fluid">
    <div class="row">
        <!-- Left Panel - Login Form -->
        <div class="col-md-5 left-panel">
            <a href="<?php echo url(''); ?>" class="back-link">
                <i class="fas fa-arrow-left"></i> Back to Main Site
            </a>

            <!-- Logo -->
            <div class="logo">
                <?php if (!empty($site_settings['site_logo']) && file_exists($site_settings['site_logo'])): ?>
                    <img src="<?php echo htmlspecialchars($site_settings['site_logo']); ?>"
                         alt="<?php echo htmlspecialchars($site_settings['site_name']); ?>"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <h2 class="logo-fallback" style="display: none;"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                <?php else: ?>
                    <h2 class="logo-fallback"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                <?php endif; ?>
            </div>

            <!-- Welcome Text -->
            <div class="welcome-text">
                <h1>Welcome Back</h1>
                <p>Please sign in to access your banking account securely</p>
            </div>

            <!-- Login Form -->
            <div class="login-form">
                <!-- Error Messages -->
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Timeout Message -->
                <?php if (isset($_GET['timeout'])): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-clock"></i>
                        Your session has expired for security reasons. Please log in again.
                    </div>
                <?php endif; ?>

                <!-- Success Messages -->
                <?php if (hasFlashMessage('success')): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars(getFlashMessage('success')); ?>
                    </div>
                <?php endif; ?>

                <!-- Info Messages -->
                <?php if (hasFlashMessage('info')): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <?php echo htmlspecialchars(getFlashMessage('info')); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="" id="loginForm">
                    <div class="form-group">
                        <label for="username" class="form-label">Username, Email, or Account Number</label>
                        <div class="input-group">
                            <i class="input-icon fas fa-user"></i>
                            <input type="text"
                                   class="form-control"
                                   id="username"
                                   name="username"
                                   placeholder="Enter your login credentials"
                                   value="<?php echo htmlspecialchars($username ?? ''); ?>"
                                   required
                                   autofocus
                                   autocomplete="username">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <i class="input-icon fas fa-lock"></i>
                            <input type="password"
                                   class="form-control"
                                   id="password"
                                   name="password"
                                   placeholder="Enter your password"
                                   required
                                   autocomplete="current-password">
                        </div>
                        <div class="forgot-password">
                            <a href="<?php echo url('auth/forgot-password.php'); ?>">Forgot your password?</a>
                        </div>
                    </div>

                    <button type="submit" class="btn-login" id="loginBtn">
                        <i class="fas fa-sign-in-alt"></i> Sign In to Account
                    </button>
                </form>

                <!-- Registration Link -->
                <div class="signup-link">
                    Don't have an account? <a href="<?php echo url('register.php'); ?>">Create Account</a>
                </div>

                <!-- Security Notice -->
                <div class="security-notice">
                    <h6><i class="fas fa-shield-alt"></i> Secure Banking</h6>
                    <p>
                        Your connection is encrypted and secure. We use industry-standard security measures 
                        to protect your personal and financial information.
                    </p>
                </div>
            </div>
        </div>

        <!-- Right Panel - Enhanced Banking Visual -->
        <div class="col-md-7 right-panel">
            <div class="right-panel-content">
                <div class="feature-illustration">
                    <div class="dashboard-svg-container">
                        <!-- Banking Dashboard Illustration -->
                        <svg class="dashboard-svg" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <!-- Gradients for modern look -->
                                <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.4"/>
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1"/>
                                </linearGradient>
                                <linearGradient id="dashboardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.5"/>
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.2"/>
                                </linearGradient>
                            </defs>

                            <!-- Banking Dashboard -->
                            <rect x="50" y="40" width="300" height="120" rx="12" fill="url(#userGradient)" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>

                            <!-- Dashboard Header -->
                            <rect x="70" y="60" width="260" height="12" rx="6" fill="rgba(255,255,255,0.6)"/>

                            <!-- Banking Cards -->
                            <rect x="70" y="85" width="80" height="60" rx="8" fill="url(#dashboardGradient)"/>
                            <rect x="160" y="85" width="80" height="60" rx="8" fill="url(#dashboardGradient)"/>
                            <rect x="250" y="85" width="80" height="60" rx="8" fill="url(#dashboardGradient)"/>

                            <!-- Card Icons -->
                            <circle cx="110" cy="105" r="8" fill="rgba(255,255,255,0.7)"/>
                            <circle cx="200" cy="105" r="8" fill="rgba(255,255,255,0.7)"/>
                            <circle cx="290" cy="105" r="8" fill="rgba(255,255,255,0.7)"/>

                            <!-- Card Text Lines -->
                            <rect x="80" y="125" width="60" height="3" rx="1" fill="rgba(255,255,255,0.5)"/>
                            <rect x="170" y="125" width="60" height="3" rx="1" fill="rgba(255,255,255,0.5)"/>
                            <rect x="260" y="125" width="60" height="3" rx="1" fill="rgba(255,255,255,0.5)"/>

                            <rect x="80" y="135" width="40" height="3" rx="1" fill="rgba(255,255,255,0.4)"/>
                            <rect x="170" y="135" width="40" height="3" rx="1" fill="rgba(255,255,255,0.4)"/>
                            <rect x="260" y="135" width="40" height="3" rx="1" fill="rgba(255,255,255,0.4)"/>

                            <!-- Banking Icon -->
                            <path d="M200 10 L220 5 L240 10 L240 30 Q240 40 220 45 Q200 40 200 30 Z" fill="rgba(255,255,255,0.6)" stroke="rgba(255,255,255,0.8)" stroke-width="2"/>
                            <path d="M210 20 L218 25 L230 15" stroke="rgba(255,255,255,0.9)" stroke-width="2" fill="none"/>

                            <!-- Floating Banking Elements -->
                            <circle cx="30" cy="30" r="3" fill="rgba(255,255,255,0.5)"/>
                            <circle cx="370" cy="25" r="2" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="20" cy="100" r="2" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="380" cy="120" r="3" fill="rgba(255,255,255,0.4)"/>
                            <circle cx="15" cy="170" r="2" fill="rgba(255,255,255,0.3)"/>
                            <circle cx="385" cy="180" r="2" fill="rgba(255,255,255,0.4)"/>

                            <!-- Connection Lines -->
                            <path d="M33 30 Q50 25 70 30" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
                            <path d="M370 28 Q350 35 330 30" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
                            <path d="M22 100 Q35 95 50 100" stroke="rgba(255,255,255,0.2)" stroke-width="1" fill="none"/>
                        </svg>
                    </div>
                </div>

                <div class="bottom-text">
                    <h3>Secure Online Banking</h3>
                    <p>
                        Access your accounts, transfer funds, pay bills, and manage your finances
                        with confidence using our secure banking platform.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Enhanced UX -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');

    // Form submission handling
    loginForm.addEventListener('submit', function(e) {
        // Add loading state
        loginBtn.classList.add('loading');
        loginBtn.disabled = true;
        
        // Basic client-side validation
        if (!usernameInput.value.trim() || !passwordInput.value.trim()) {
            e.preventDefault();
            loginBtn.classList.remove('loading');
            loginBtn.disabled = false;
            
            // Show error
            showAlert('Please fill in all required fields.', 'danger');
            return;
        }
    });

    // Input validation feedback
    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.style.borderColor = '#dc2626';
            } else {
                this.style.borderColor = '#d1d5db';
            }
        });

        input.addEventListener('input', function() {
            if (this.style.borderColor === 'rgb(220, 38, 38)') {
                this.style.borderColor = '#d1d5db';
            }
        });
    });

    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (!alert.classList.contains('alert-danger')) {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            }, 5000);
        }
    });

    function showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        
        const form = document.querySelector('.login-form');
        form.insertBefore(alertDiv, form.firstChild);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            alertDiv.style.opacity = '0';
            setTimeout(() => alertDiv.remove(), 300);
        }, 5000);
    }
});
</script>
