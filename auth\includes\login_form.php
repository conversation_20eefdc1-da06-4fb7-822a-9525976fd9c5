<?php
/**
 * User Login Form Component
 * Contains the login form with validation and error handling
 */
?>

<div class="container-fluid">
    <div class="row">
        <!-- Left Panel - Login Form -->
        <div class="col-md-5 left-panel">
            <a href="<?php echo url(''); ?>" class="back-link">
                <i class="fas fa-arrow-left"></i> Back to Main Site
            </a>

            <!-- Logo -->
            <div class="logo">
                <?php if (!empty($site_settings['site_logo']) && file_exists($site_settings['site_logo'])): ?>
                    <img src="<?php echo htmlspecialchars($site_settings['site_logo']); ?>"
                         alt="<?php echo htmlspecialchars($site_settings['site_name']); ?>"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <h2 class="logo-fallback" style="display: none;"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                <?php else: ?>
                    <h2 class="logo-fallback"><?php echo htmlspecialchars($site_settings['site_name']); ?></h2>
                <?php endif; ?>
            </div>

            <!-- Welcome Text -->
            <div class="welcome-text">
                <h1>Welcome Back</h1>
                <p>Please sign in to access your banking account securely</p>
            </div>

            <!-- Login Form -->
            <div class="login-form">
                <!-- Error Messages -->
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Timeout Message -->
                <?php if (isset($_GET['timeout'])): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-clock"></i>
                        Your session has expired for security reasons. Please log in again.
                    </div>
                <?php endif; ?>

                <!-- Success Messages -->
                <?php if (hasFlashMessage('success')): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars(getFlashMessage('success')); ?>
                    </div>
                <?php endif; ?>

                <!-- Info Messages -->
                <?php if (hasFlashMessage('info')): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <?php echo htmlspecialchars(getFlashMessage('info')); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="" id="loginForm">
                    <div class="form-group">
                        <label for="username" class="form-label">Username, Email, or Account Number</label>
                        <div class="input-group">
                            <i class="input-icon fas fa-user"></i>
                            <input type="text"
                                   class="form-control"
                                   id="username"
                                   name="username"
                                   placeholder="Enter your login credentials"
                                   value="<?php echo htmlspecialchars($username ?? ''); ?>"
                                   required
                                   autofocus
                                   autocomplete="username">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <i class="input-icon fas fa-lock"></i>
                            <input type="password"
                                   class="form-control"
                                   id="password"
                                   name="password"
                                   placeholder="Enter your password"
                                   required
                                   autocomplete="current-password">
                        </div>
                        <div class="forgot-password">
                            <a href="<?php echo url('auth/forgot-password.php'); ?>">Forgot your password?</a>
                        </div>
                    </div>

                    <button type="submit" class="btn-login" id="loginBtn">
                        <i class="fas fa-sign-in-alt"></i> Sign In to Account
                    </button>
                </form>

                <!-- Registration Link -->
                <div class="signup-link">
                    Don't have an account? <a href="<?php echo url('register.php'); ?>">Create Account</a>
                </div>

                <!-- Security Notice -->
                <div class="security-notice">
                    <h6><i class="fas fa-shield-alt"></i> Secure Banking</h6>
                    <p>
                        Your connection is encrypted and secure. We use industry-standard security measures 
                        to protect your personal and financial information.
                    </p>
                </div>
            </div>
        </div>

        <!-- Right Panel - Enhanced Banking Visual -->
        <div class="col-md-7 right-panel">
            <div class="right-panel-content">
                <div class="feature-illustration">
                    <div class="dashboard-svg-container">
                        <!-- Enhanced Banking Dashboard Illustration with Animations -->
                        <svg class="dashboard-svg" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <!-- Enhanced Gradients for modern look -->
                                <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.6"/>
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.2"/>
                                </linearGradient>
                                <linearGradient id="dashboardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.7"/>
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.3"/>
                                </linearGradient>
                                <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8"/>
                                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.4"/>
                                </linearGradient>
                                <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8"/>
                                    <stop offset="100%" style="stop-color:#059669;stop-opacity:0.6"/>
                                </linearGradient>
                                <!-- Filters for glow effects -->
                                <filter id="glow">
                                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                    <feMerge>
                                        <feMergeNode in="coloredBlur"/>
                                        <feMergeNode in="SourceGraphic"/>
                                    </feMerge>
                                </filter>
                            </defs>

                            <!-- Main Banking Dashboard Container -->
                            <rect x="50" y="40" width="300" height="120" rx="12" fill="url(#userGradient)" stroke="rgba(255,255,255,0.5)" stroke-width="2" class="dashboard-main">
                                <animate attributeName="opacity" values="0.8;1;0.8" dur="4s" repeatCount="indefinite"/>
                            </rect>

                            <!-- Animated Credit Cards -->
                            <g class="credit-cards">
                                <!-- Card 1 - Checking Account -->
                                <rect x="70" y="85" width="80" height="50" rx="8" fill="url(#cardGradient)" class="banking-card">
                                    <animateTransform attributeName="transform" type="translate" values="0,0; 0,-2; 0,0" dur="3s" repeatCount="indefinite"/>
                                </rect>
                                <circle cx="90" cy="105" r="6" fill="rgba(255,255,255,0.9)">
                                    <animate attributeName="r" values="6;8;6" dur="2s" repeatCount="indefinite"/>
                                </circle>
                                <text x="105" y="110" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Arial">CHECKING</text>
                                <text x="75" y="125" fill="rgba(255,255,255,0.7)" font-size="6" font-family="Arial">****1234</text>

                                <!-- Card 2 - Savings Account -->
                                <rect x="160" y="85" width="80" height="50" rx="8" fill="url(#cardGradient)" class="banking-card">
                                    <animateTransform attributeName="transform" type="translate" values="0,0; 0,-2; 0,0" dur="3.5s" repeatCount="indefinite"/>
                                </rect>
                                <circle cx="180" cy="105" r="6" fill="rgba(255,255,255,0.9)">
                                    <animate attributeName="r" values="6;8;6" dur="2.5s" repeatCount="indefinite"/>
                                </circle>
                                <text x="195" y="110" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Arial">SAVINGS</text>
                                <text x="165" y="125" fill="rgba(255,255,255,0.7)" font-size="6" font-family="Arial">****5678</text>

                                <!-- Card 3 - Credit Card -->
                                <rect x="250" y="85" width="80" height="50" rx="8" fill="url(#cardGradient)" class="banking-card">
                                    <animateTransform attributeName="transform" type="translate" values="0,0; 0,-2; 0,0" dur="4s" repeatCount="indefinite"/>
                                </rect>
                                <circle cx="270" cy="105" r="6" fill="rgba(255,255,255,0.9)">
                                    <animate attributeName="r" values="6;8;6" dur="3s" repeatCount="indefinite"/>
                                </circle>
                                <text x="285" y="110" fill="rgba(255,255,255,0.8)" font-size="8" font-family="Arial">CREDIT</text>
                                <text x="255" y="125" fill="rgba(255,255,255,0.7)" font-size="6" font-family="Arial">****9012</text>
                            </g>

                            <!-- Security Shield with Animation -->
                            <g class="security-shield" transform="translate(200, 10)">
                                <path d="M0 0 L15 -5 L30 0 L30 20 Q30 30 15 35 Q0 30 0 20 Z" fill="url(#shieldGradient)" filter="url(#glow)">
                                    <animateTransform attributeName="transform" type="scale" values="1;1.1;1" dur="3s" repeatCount="indefinite"/>
                                </path>
                                <path d="M10 10 L13 13 L20 5" stroke="rgba(255,255,255,0.9)" stroke-width="2" fill="none">
                                    <animate attributeName="stroke-dasharray" values="0,20;20,0;0,20" dur="2s" repeatCount="indefinite"/>
                                </path>
                            </g>

                            <!-- Money Transfer Animation -->
                            <g class="money-transfer">
                                <circle cx="30" cy="60" r="8" fill="rgba(255,255,255,0.6)" class="money-dot">
                                    <animateTransform attributeName="transform" type="translate" values="0,0; 340,0; 0,0" dur="6s" repeatCount="indefinite"/>
                                </circle>
                                <path d="M30 60 L370 60" stroke="rgba(255,255,255,0.3)" stroke-width="2" stroke-dasharray="5,5">
                                    <animate attributeName="stroke-dashoffset" values="0;-10" dur="1s" repeatCount="indefinite"/>
                                </path>
                                <text x="200" y="55" fill="rgba(255,255,255,0.7)" font-size="8" text-anchor="middle" font-family="Arial">SECURE TRANSFER</text>
                            </g>

                            <!-- Floating Data Points with Animation -->
                            <g class="floating-elements">
                                <circle cx="30" cy="30" r="3" fill="rgba(255,255,255,0.6)">
                                    <animate attributeName="cy" values="30;25;30" dur="2s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="370" cy="25" r="2" fill="rgba(255,255,255,0.5)">
                                    <animate attributeName="cy" values="25;20;25" dur="2.5s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="20" cy="170" r="2" fill="rgba(255,255,255,0.4)">
                                    <animate attributeName="cy" values="170;165;170" dur="3s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="380" cy="180" r="3" fill="rgba(255,255,255,0.5)">
                                    <animate attributeName="cy" values="180;175;180" dur="2.2s" repeatCount="indefinite"/>
                                </circle>
                            </g>

                            <!-- Chart/Analytics Animation -->
                            <g class="analytics-chart" transform="translate(320, 140)">
                                <rect x="0" y="0" width="60" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)"/>
                                <polyline points="5,30 15,20 25,25 35,15 45,18 55,10" stroke="rgba(255,255,255,0.8)" stroke-width="2" fill="none">
                                    <animate attributeName="stroke-dasharray" values="0,100;100,0" dur="4s" repeatCount="indefinite"/>
                                </polyline>
                                <circle cx="55" cy="10" r="2" fill="rgba(255,255,255,0.9)">
                                    <animate attributeName="r" values="2;4;2" dur="1s" repeatCount="indefinite"/>
                                </circle>
                            </g>
                        </svg>
                    </div>
                </div>

                <div class="bottom-text">
                    <h3>Secure Online Banking</h3>
                    <p>
                        Access your accounts, transfer funds, pay bills, and manage your finances
                        with confidence using our secure banking platform.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Enhanced UX -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');

    // Form submission handling
    loginForm.addEventListener('submit', function(e) {
        // Add loading state
        loginBtn.classList.add('loading');
        loginBtn.disabled = true;
        
        // Basic client-side validation
        if (!usernameInput.value.trim() || !passwordInput.value.trim()) {
            e.preventDefault();
            loginBtn.classList.remove('loading');
            loginBtn.disabled = false;
            
            // Show error
            showAlert('Please fill in all required fields.', 'danger');
            return;
        }
    });

    // Input validation feedback
    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.style.borderColor = '#dc2626';
            } else {
                this.style.borderColor = '#d1d5db';
            }
        });

        input.addEventListener('input', function() {
            if (this.style.borderColor === 'rgb(220, 38, 38)') {
                this.style.borderColor = '#d1d5db';
            }
        });
    });

    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (!alert.classList.contains('alert-danger')) {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            }, 5000);
        }
    });

    function showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;

        const form = document.querySelector('.login-form');
        form.insertBefore(alertDiv, form.firstChild);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            alertDiv.style.opacity = '0';
            setTimeout(() => alertDiv.remove(), 300);
        }, 5000);
    }

    // Mobile responsive layout creation
    function createMobileLayout() {
        if (window.innerWidth <= 768) {
            // Create mobile container
            const mobileContainer = document.createElement('div');
            mobileContainer.className = 'mobile-login-container';

            // Get site settings for logo
            const siteSettings = <?php echo json_encode($site_settings ?? ['site_name' => 'SecureBank', 'site_logo' => '']); ?>;

            mobileContainer.innerHTML = `
                <div class="mobile-logo">
                    ${siteSettings.site_logo ?
                        `<img src="${siteSettings.site_logo}" alt="${siteSettings.site_name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                         <div class="mobile-logo-fallback" style="display: none;">
                             <i class="fas fa-university"></i>
                         </div>` :
                        `<div class="mobile-logo-fallback">
                             <i class="fas fa-university"></i>
                         </div>`
                    }
                </div>

                <h1 class="mobile-title">Welcome Back</h1>
                <p class="mobile-subtitle">Sign in to access your secure banking dashboard</p>

                <form method="POST" action="" id="mobileLoginForm">
                    <div class="mobile-form-group">
                        <label for="mobile-username" class="mobile-form-label">Username, Email, or Account Number</label>
                        <div class="mobile-input-group">
                            <i class="mobile-input-icon fas fa-user"></i>
                            <input type="text"
                                   class="mobile-form-control"
                                   id="mobile-username"
                                   name="username"
                                   placeholder="Enter your login credentials"
                                   value="${document.getElementById('username')?.value || ''}"
                                   required
                                   autofocus
                                   autocomplete="username">
                        </div>
                    </div>

                    <div class="mobile-form-group">
                        <label for="mobile-password" class="mobile-form-label">Password</label>
                        <div class="mobile-input-group">
                            <i class="mobile-input-icon fas fa-lock"></i>
                            <input type="password"
                                   class="mobile-form-control"
                                   id="mobile-password"
                                   name="password"
                                   placeholder="Enter your password"
                                   required
                                   autocomplete="current-password">
                        </div>
                        <div class="mobile-forgot-password">
                            <a href="<?php echo url('auth/forgot-password.php'); ?>">Forgot your password?</a>
                        </div>
                    </div>

                    <button type="submit" class="mobile-login-button" id="mobileLoginBtn">
                        <i class="fas fa-sign-in-alt"></i> Sign In to Account
                    </button>
                </form>

                <div class="mobile-signup-link">
                    Don't have an account? <a href="<?php echo url('register.php'); ?>">Create Account</a>
                </div>

                <div class="mobile-security-notice">
                    <h6><i class="fas fa-shield-alt"></i> Secure Banking</h6>
                    <p>Your connection is encrypted and secure. We use industry-standard security measures to protect your information.</p>
                </div>
            `;

            // Replace body content with mobile layout
            document.body.innerHTML = '';
            document.body.appendChild(mobileContainer);

            // Re-attach form handlers for mobile
            attachMobileFormHandlers();
        }
    }

    function attachMobileFormHandlers() {
        const mobileForm = document.getElementById('mobileLoginForm');
        const mobileBtn = document.getElementById('mobileLoginBtn');
        const mobileUsernameInput = document.getElementById('mobile-username');
        const mobilePasswordInput = document.getElementById('mobile-password');

        if (mobileForm) {
            mobileForm.addEventListener('submit', function(e) {
                mobileBtn.classList.add('loading');
                mobileBtn.disabled = true;
                mobileBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';

                if (!mobileUsernameInput.value.trim() || !mobilePasswordInput.value.trim()) {
                    e.preventDefault();
                    mobileBtn.classList.remove('loading');
                    mobileBtn.disabled = false;
                    mobileBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Sign In to Account';
                    showMobileAlert('Please fill in all required fields.', 'error');
                    return;
                }
            });

            // Input validation for mobile
            [mobileUsernameInput, mobilePasswordInput].forEach(input => {
                if (input) {
                    input.addEventListener('focus', function() {
                        this.style.borderColor = '#6366f1';
                    });

                    input.addEventListener('blur', function() {
                        if (this.value.trim() === '') {
                            this.style.borderColor = '#dc2626';
                        } else {
                            this.style.borderColor = '#e5e7eb';
                        }
                    });
                }
            });
        }
    }

    function showMobileAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `mobile-alert mobile-alert-${type}`;
        alertDiv.innerHTML = `<i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'check-circle'}"></i> ${message}`;

        const container = document.querySelector('.mobile-login-container');
        if (container) {
            container.insertBefore(alertDiv, container.querySelector('form'));

            // Auto-hide after 5 seconds
            setTimeout(() => {
                alertDiv.style.opacity = '0';
                setTimeout(() => alertDiv.remove(), 300);
            }, 5000);
        }
    }

    // Initialize mobile layout on load and resize
    createMobileLayout();
    window.addEventListener('resize', createMobileLayout);
});
</script>
