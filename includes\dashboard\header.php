<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title . ' - ' . $site_name); ?></title>

    <!-- Custom Banking CSS -->
    <link href="style.css" rel="stylesheet">

    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1f2937;
        }

        /* Banking Container Styles */
        .banking-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0;
        }

        .banking-dashboard {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        /* Page Header */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title h1 {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .page-title p {
            color: #6b7280;
            font-size: 1rem;
        }

        .page-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Button Styles */
        .btn-primary, .btn-outline, .btn-secondary {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            border: 1px solid transparent;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: #6366f1;
            color: white;
            border-color: #6366f1;
        }

        .btn-primary:hover {
            background: #5856eb;
            border-color: #5856eb;
            text-decoration: none;
            color: white;
        }

        .btn-outline {
            background: white;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
            text-decoration: none;
            color: #374151;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border-color: #e5e7eb;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
            text-decoration: none;
            color: #374151;
        }

        /* Card Styles */
        .card, .stat-card, .balance-card, .account-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .card-body, .card-content {
            padding: 1.5rem;
        }

        /* Grid Layouts */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .cards-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        /* Stat Card Styles */
        .stat-card {
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f3f4f6;
            color: #6366f1;
            flex-shrink: 0;
        }

        .stat-icon.balance {
            background: #ecfdf5;
            color: #10b981;
        }

        .stat-icon.limit {
            background: #fef3c7;
            color: #f59e0b;
        }

        .stat-icon.spending {
            background: #fce7f3;
            color: #ec4899;
        }

        .stat-content h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .stat-content p {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .stat-details {
            display: flex;
            gap: 1rem;
            font-size: 0.75rem;
        }

        .active-count {
            color: #10b981;
            font-weight: 500;
        }

        .blocked-count {
            color: #ef4444;
            font-weight: 500;
        }

        /* Account Overview Styles */
        .account-overview {
            margin-bottom: 2rem;
        }

        .primary-account-card {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: 2rem;
            border-radius: 16px;
            margin-bottom: 2rem;
        }

        .account-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
        }

        .account-info h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .account-number {
            opacity: 0.9;
            font-family: 'Courier New', monospace;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .status-badge.active {
            background: #10b981;
            color: white;
        }

        .balance-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }

        .current-balance h3 {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 0.5rem;
        }

        .balance-amount {
            font-size: 3rem;
            font-weight: 700;
            line-height: 1;
        }

        .balance-actions {
            display: flex;
            gap: 1rem;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .action-btn.primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .action-btn.primary:hover {
            background: rgba(255, 255, 255, 0.3);
            text-decoration: none;
            color: white;
        }

        .action-btn.secondary {
            background: white;
            color: #6366f1;
            border: 1px solid white;
        }

        .action-btn.secondary:hover {
            background: #f8fafc;
            text-decoration: none;
            color: #6366f1;
        }

        /* Banking Sidebar Styles */
        .banking-sidebar {
            background: #ffffff;
            width: 320px;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            border-right: 1px solid #e5e7eb;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .bank-logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .logo-icon {
            width: 48px;
            height: 48px;
            background: #6366f1;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #60a5fa;
            backdrop-filter: blur(10px);
        }

        .bank-name {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1f2937;
            line-height: 1.2;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: #6366f1;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: white;
            font-size: 0.875rem;
        }

        .user-details {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-size: 0.875rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .account-number {
            font-size: 0.75rem;
            color: #6b7280;
            font-family: 'Courier New', monospace;
        }

        .sidebar-nav {
            flex: 1;
            padding: 1rem 0;
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: #9ca3af;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            padding: 0 1.5rem 0.75rem;
            margin-bottom: 0.5rem;
        }

        .nav-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: #6b7280;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            font-weight: 500;
            position: relative;
        }

        .nav-link:hover {
            background: #f3f4f6;
            color: #6366f1;
            text-decoration: none;
            transform: translateX(4px);
        }

        .nav-link.active {
            background: #6366f1;
            color: white;
            border: 1px solid #6366f1;
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: #60a5fa;
            border-radius: 0 2px 2px 0;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }

        .sidebar-footer {
            padding: 1.5rem;
            border-top: 1px solid #e5e7eb;
        }

        .logout-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: #f87171;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            font-weight: 500;
            background: rgba(248, 113, 113, 0.1);
            border: 1px solid rgba(248, 113, 113, 0.2);
        }

        .logout-link:hover {
            background: rgba(248, 113, 113, 0.2);
            color: #f87171;
            text-decoration: none;
            transform: translateX(4px);
        }

        .logout-icon {
            width: 20px;
            height: 20px;
        }

        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            padding: 2rem;
            background: #f8fafc;
        }

        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .top-bar h1 {
            font-size: 2rem;
            font-weight: 600;
            margin: 0;
            color: #1a1a1a;
        }

        .top-bar-actions {
            display: flex;
            gap: 1rem;
        }

        .btn-primary {
            background: #6366f1;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-outline {
            background: transparent;
            border: 1px solid #d1d5db;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            color: #374151;
            font-weight: 500;
            text-decoration: none;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }

        .main-section {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .sidebar-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .balance-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e5e7eb;
        }

        .balance-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .balance-title {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .balance-amount {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1a1a1a;
            margin: 0.5rem 0;
        }

        .balance-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .balance-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid #e5e7eb;
            background: white;
            color: #374151;
        }

        .quick-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .quick-action {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .quick-action-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .stat-title {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0 0 0.5rem;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0;
        }

        .stat-change {
            font-size: 0.75rem;
            color: #10b981;
            margin-top: 0.25rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0;
        }

        .card-body {
            padding: 0;
        }

        .virtual-card {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .virtual-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
        }

        .card-brand {
            text-align: right;
            font-weight: 700;
            font-size: 1.25rem;
            margin-bottom: 1rem;
        }

        .card-number {
            font-family: 'Courier New', monospace;
            font-size: 1.125rem;
            letter-spacing: 2px;
            margin-bottom: 1rem;
        }

        .card-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
        }

        .conversion-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .conversion-title {
            font-size: 1rem;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0 0 1rem;
        }

        .conversion-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .conversion-amount {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .conversion-currency {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .conversion-btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            width: 100%;
            margin-top: 1rem;
        }

        .workflows-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .workflow-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 0.75rem;
            background: #f9fafb;
        }

        .workflow-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #1f2937;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .workflow-content {
            flex: 1;
        }

        .workflow-title {
            font-weight: 600;
            color: #1a1a1a;
            margin: 0 0 0.25rem;
        }

        .workflow-subtitle {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .transaction-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            color: white;
        }

        .transaction-content {
            flex: 1;
        }

        .transaction-title {
            font-weight: 500;
            color: #1a1a1a;
            margin: 0 0 0.25rem;
        }

        .transaction-subtitle {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .transaction-amount {
            font-weight: 600;
            text-align: right;
        }

        .transaction-date {
            font-size: 0.75rem;
            color: #9ca3af;
            text-align: right;
            margin-top: 0.25rem;
        }

        .amount-positive {
            color: #10b981;
        }

        .amount-negative {
            color: #ef4444;
        }

        .status-completed {
            color: #10b981;
            background: #d1fae5;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-pending {
            color: #f59e0b;
            background: #fef3c7;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Form Styles */
        .form-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .form-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            background: #f8fafc;
        }

        .form-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .form-header p {
            color: #6b7280;
            margin: 0;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            padding: 1.5rem;
            background: #f8fafc;
            border-top: 1px solid #e5e7eb;
        }

        .amount-input {
            position: relative;
            display: flex;
            align-items: center;
        }

        .currency-symbol {
            position: absolute;
            left: 0.75rem;
            color: #6b7280;
            font-weight: 500;
            z-index: 1;
        }

        .amount-input .form-control {
            padding-left: 3rem;
        }

        /* Alert Styles */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .alert-warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }

        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        /* Empty State Styles */
        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: #6b7280;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 1rem;
            color: #d1d5db;
        }

        .empty-state h4 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .empty-state p {
            margin-bottom: 1.5rem;
        }

        .empty-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        /* Table Styles */
        .table-container {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #f3f4f6;
        }

        .table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
            font-size: 0.875rem;
        }

        .table tbody tr:hover {
            background: #f9fafb;
        }

        /* Banking Sidebar Responsive Design */
        @media (max-width: 1024px) {
            .banking-sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .bank-name {
                font-size: 1.125rem;
            }
        }

        @media (max-width: 768px) {
            .banking-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 280px;
            }

            .banking-sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }

            .sidebar-header {
                padding: 1.5rem;
            }

            .user-info {
                padding: 0.75rem;
            }

            .nav-section-title {
                padding: 0 1rem 0.5rem;
            }

            .nav-item {
                margin: 0.25rem 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .banking-sidebar {
                width: 100%;
            }

            .bank-name {
                font-size: 1rem;
            }

            .user-name {
                font-size: 0.8125rem;
            }

            .account-number {
                font-size: 0.6875rem;
            }
        }
    </style>
</head>
<body>
