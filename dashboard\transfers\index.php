<?php
require_once '../../config/config.php';
requireLogin();

$page_title = 'Transfer Money';
$site_name = getBankName();

// Handle transfer form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];
        
        $recipient_account = sanitizeInput($_POST['recipient_account']);
        $amount = floatval($_POST['amount']);
        $description = sanitizeInput($_POST['description']);
        $transfer_type = sanitizeInput($_POST['transfer_type'] ?? 'internal');
        
        // Validation
        if (empty($recipient_account) || $amount <= 0) {
            throw new Exception('Please provide valid recipient account and amount.');
        }
        
        // Check user balance
        $balance_sql = "SELECT balance FROM accounts WHERE id = ?";
        $balance_result = $db->query($balance_sql, [$user_id]);
        $user_balance = $balance_result->fetch_assoc()['balance'];
        
        if ($amount > $user_balance) {
            throw new Exception('Insufficient balance for this transfer.');
        }
        
        // Create transfer record
        $transfer_sql = "INSERT INTO transfers (sender_id, recipient_account, amount, description, transfer_type, status) 
                        VALUES (?, ?, ?, ?, ?, 'pending')";
        $result = $db->query($transfer_sql, [$user_id, $recipient_account, $amount, $description, $transfer_type]);
        
        if ($result) {
            $success_message = 'Transfer initiated successfully! It will be processed shortly.';
        } else {
            throw new Exception('Failed to initiate transfer.');
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user balance and recent beneficiaries
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    $balance_sql = "SELECT balance, currency FROM accounts WHERE id = ?";
    $balance_result = $db->query($balance_sql, [$user_id]);
    $account_info = $balance_result->fetch_assoc();
    
    // Get recent beneficiaries
    $beneficiaries_sql = "SELECT * FROM beneficiaries WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
    $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
    $recent_beneficiaries = [];
    while ($row = $beneficiaries_result->fetch_assoc()) {
        $recent_beneficiaries[] = $row;
    }
    
} catch (Exception $e) {
    error_log("Transfers page error: " . $e->getMessage());
    $account_info = ['balance' => 0, 'currency' => 'USD'];
    $recent_beneficiaries = [];
}

include '../../includes/dashboard/header.php';
include '../../includes/dashboard/sidebar.php';
?>

<div class="main-content">
    <div class="banking-container">
        <div class="banking-dashboard">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1>Transfer Money</h1>
                    <p>Send money to accounts and beneficiaries</p>
                </div>
                <div class="page-actions">
                    <a href="../beneficiaries/" class="btn-outline">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                        Manage Beneficiaries
                    </a>
                    <a href="../transactions/" class="btn-primary">
                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                        Transaction History
                    </a>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Transfer Form -->
            <div class="dashboard-grid">
                <div class="main-section">
                    <div class="form-card">
                        <div class="form-header">
                            <h3>Send Money</h3>
                            <p>Transfer funds to another account</p>
                        </div>
                        <form method="POST" style="padding: 2rem;">
                            <div class="form-group">
                                <label for="recipient_account">Recipient Account Number</label>
                                <input type="text" id="recipient_account" name="recipient_account" class="form-control" 
                                       placeholder="Enter account number" required>
                            </div>

                            <div class="form-group">
                                <label for="amount">Amount</label>
                                <div class="amount-input">
                                    <span class="currency-symbol"><?php echo $account_info['currency'] ?? 'USD'; ?></span>
                                    <input type="number" id="amount" name="amount" class="form-control" 
                                           placeholder="0.00" step="0.01" min="0.01" required>
                                </div>
                                <small style="color: #6b7280; font-size: 0.875rem;">
                                    Available balance: <?php echo formatCurrency($account_info['balance'], $account_info['currency'] ?? 'USD'); ?>
                                </small>
                            </div>

                            <div class="form-group">
                                <label for="transfer_type">Transfer Type</label>
                                <select id="transfer_type" name="transfer_type" class="form-control">
                                    <option value="internal">Internal Transfer</option>
                                    <option value="external">External Transfer</option>
                                    <option value="international">International Transfer</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="description">Description (Optional)</label>
                                <textarea id="description" name="description" class="form-control" 
                                          placeholder="Enter transfer description" rows="3"></textarea>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn-outline" onclick="history.back()">Cancel</button>
                                <button type="submit" class="btn-primary">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                                    </svg>
                                    Send Money
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="sidebar-section">
                    <!-- Recent Beneficiaries -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Recent Beneficiaries</h3>
                            <a href="../beneficiaries/" style="color: #6366f1; font-size: 0.875rem; text-decoration: none;">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recent_beneficiaries)): ?>
                                <?php foreach ($recent_beneficiaries as $beneficiary): ?>
                                    <div class="beneficiary-item" style="padding: 1rem; border-bottom: 1px solid #f3f4f6; cursor: pointer;" 
                                         onclick="fillBeneficiaryData('<?php echo htmlspecialchars($beneficiary['account_number']); ?>', '<?php echo htmlspecialchars($beneficiary['name']); ?>')">
                                        <div style="display: flex; align-items: center; gap: 1rem;">
                                            <div style="width: 40px; height: 40px; background: #6366f1; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                                                <?php echo strtoupper(substr($beneficiary['name'], 0, 2)); ?>
                                            </div>
                                            <div>
                                                <div style="font-weight: 600; color: #1f2937;"><?php echo htmlspecialchars($beneficiary['name']); ?></div>
                                                <div style="font-size: 0.875rem; color: #6b7280;"><?php echo htmlspecialchars($beneficiary['bank_name']); ?></div>
                                                <div style="font-size: 0.75rem; color: #9ca3af; font-family: monospace;">****<?php echo substr($beneficiary['account_number'], -4); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div style="padding: 2rem; text-align: center; color: #6b7280;">
                                    <p>No beneficiaries found</p>
                                    <a href="../beneficiaries/" style="color: #6366f1; text-decoration: none;">Add your first beneficiary</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Transfer Tips -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Transfer Tips</h3>
                        </div>
                        <div style="padding: 1.5rem;">
                            <div style="display: flex; flex-direction: column; gap: 1rem;">
                                <div style="display: flex; gap: 0.75rem;">
                                    <svg width="20" height="20" fill="#10b981" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    <div>
                                        <div style="font-weight: 600; font-size: 0.875rem;">Double-check account numbers</div>
                                        <div style="font-size: 0.75rem; color: #6b7280;">Verify recipient details before sending</div>
                                    </div>
                                </div>
                                <div style="display: flex; gap: 0.75rem;">
                                    <svg width="20" height="20" fill="#10b981" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    <div>
                                        <div style="font-weight: 600; font-size: 0.875rem;">Internal transfers are instant</div>
                                        <div style="font-size: 0.75rem; color: #6b7280;">Same-bank transfers process immediately</div>
                                    </div>
                                </div>
                                <div style="display: flex; gap: 0.75rem;">
                                    <svg width="20" height="20" fill="#10b981" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    <div>
                                        <div style="font-weight: 600; font-size: 0.875rem;">Save frequent recipients</div>
                                        <div style="font-size: 0.75rem; color: #6b7280;">Add to beneficiaries for quick access</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function fillBeneficiaryData(accountNumber, name) {
    document.getElementById('recipient_account').value = accountNumber;
    document.getElementById('description').value = 'Transfer to ' + name;
}
</script>

<?php include '../../includes/dashboard/footer.php'; ?>
