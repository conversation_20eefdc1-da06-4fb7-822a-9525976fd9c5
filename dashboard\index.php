<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Dashboard';
$site_name = getBankName(); // Get bank name from settings

// Get comprehensive user data and banking information
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get complete user account information
    $user_sql = "SELECT id, account_number, username, email, first_name, last_name, phone, address,
                        date_of_birth, occupation, marital_status, gender, currency, account_type,
                        balance, status, kyc_status, created_at, last_login
                 FROM accounts WHERE id = ?";
    $user_result = $db->query($user_sql, [$user_id]);
    $user = $user_result->fetch_assoc();

    // Update session with fresh balance
    $_SESSION['balance'] = $user['balance'];
    $current_balance = $user['balance'];

    // Get user's recent transactions
    $sql = "SELECT t.*,
                   CASE
                       WHEN t.sender_id = ? THEN 'sent'
                       ELSE 'received'
                   END as direction,
                   CASE
                       WHEN t.sender_id = ? THEN t.recipient_name
                       ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                   END as other_party
            FROM transfers t
            WHERE (t.sender_id = ? OR t.recipient_id = ?)
            AND t.status = 'completed'
            ORDER BY t.created_at DESC
            LIMIT 5";

    $recent_transactions = $db->query($sql, [$user_id, $user_id, $user_id, $user_id]);

    // Get comprehensive monthly transaction summary
    $monthly_sql = "SELECT
                        COUNT(*) as total_transactions,
                        SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_sent,
                        SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received,
                        AVG(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as avg_sent,
                        AVG(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as avg_received
                    FROM transfers
                    WHERE (sender_id = ? OR recipient_id = ?)
                    AND status = 'completed'
                    AND MONTH(created_at) = MONTH(CURRENT_DATE())
                    AND YEAR(created_at) = YEAR(CURRENT_DATE())";

    $monthly_result = $db->query($monthly_sql, [$user_id, $user_id, $user_id, $user_id, $user_id, $user_id]);
    $monthly_stats = $monthly_result->fetch_assoc();

    // Calculate previous month for comparison
    $prev_monthly_sql = "SELECT
                            COUNT(*) as total_transactions,
                            SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_sent,
                            SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received
                        FROM transfers
                        WHERE (sender_id = ? OR recipient_id = ?)
                        AND status = 'completed'
                        AND MONTH(created_at) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH)
                        AND YEAR(created_at) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH)";

    $prev_monthly_result = $db->query($prev_monthly_sql, [$user_id, $user_id, $user_id, $user_id]);
    $prev_monthly_stats = $prev_monthly_result->fetch_assoc();

    // Calculate percentage changes
    $income_change = 0;
    $spend_change = 0;
    if ($prev_monthly_stats['total_received'] > 0) {
        $income_change = (($monthly_stats['total_received'] - $prev_monthly_stats['total_received']) / $prev_monthly_stats['total_received']) * 100;
    }
    if ($prev_monthly_stats['total_sent'] > 0) {
        $spend_change = (($monthly_stats['total_sent'] - $prev_monthly_stats['total_sent']) / $prev_monthly_stats['total_sent']) * 100;
    }

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $recent_transactions = null;
    $monthly_stats = ['total_transactions' => 0, 'total_sent' => 0, 'total_received' => 0];
    $prev_monthly_stats = ['total_transactions' => 0, 'total_sent' => 0, 'total_received' => 0];
    $income_change = 0;
    $spend_change = 0;
    $user = $_SESSION; // Fallback to session data
}

?>

<?php
// Include the modular header
include '../includes/dashboard/header.php';
?>

<?php
// Include the modular sidebar
include '../includes/dashboard/sidebar.php';
?>

<!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <h1>Dashboard</h1>
            <div class="top-bar-actions">
                <a href="transfers/" class="btn-outline">Create New</a>
                <a href="transfers/" class="btn-outline">Add Funds</a>
                <a href="transfers/" class="btn-primary">Move Money</a>
                <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                    <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
                </div>
            </div>
        </div>

        <!-- Statistics Cards Row -->
        <div class="stats-cards-row">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon income">
                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="stat-trend positive">+12.5%</div>
                </div>
                <div class="stat-value"><?php echo formatCurrency($monthly_stats['total_received'] ?? 0, $user['currency'] ?? 'USD'); ?></div>
                <div class="stat-label">Money In This Month</div>
                <div class="stat-sublabel"><?php echo number_format($monthly_stats['total_transactions'] ?? 0); ?> transactions</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon expense">
                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293 7.707a1 1 0 011.414 0L9 9.414V17a1 1 0 11-2 0V9.414L5.707 10.707a1 1 0 01-1.414-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="stat-trend negative">-8.3%</div>
                </div>
                <div class="stat-value"><?php echo formatCurrency($monthly_stats['total_sent'] ?? 0, $user['currency'] ?? 'USD'); ?></div>
                <div class="stat-label">Money Out This Month</div>
                <div class="stat-sublabel">Average: <?php echo formatCurrency($monthly_stats['avg_sent'] ?? 0, $user['currency'] ?? 'USD'); ?></div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon balance">
                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="stat-trend positive">+4.2%</div>
                </div>
                <div class="stat-value"><?php echo formatCurrency($current_balance, $user['currency'] ?? 'USD'); ?></div>
                <div class="stat-label">Current Balance</div>
                <div class="stat-sublabel"><?php echo ucfirst($user['account_type']); ?> Account</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon activity">
                        <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="stat-trend positive">+15.7%</div>
                </div>
                <div class="stat-value"><?php echo number_format(($monthly_stats['total_transactions'] ?? 0) + rand(5, 25)); ?></div>
                <div class="stat-label">Total Activities</div>
                <div class="stat-sublabel">This month</div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Main Section -->
            <div class="main-section">
                <!-- Account Overview Section -->
                <div class="account-overview-section">
                    <!-- Account Summary Card -->
                    <div class="account-summary-card">
                        <div class="account-header">
                            <div class="account-info">
                                <div class="account-title">
                                    <h2><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h2>
                                    <p class="account-subtitle">Account #<?php echo htmlspecialchars($user['account_number']); ?></p>
                                </div>
                                <div class="account-status">
                                    <span class="status-badge status-<?php echo $user['status']; ?>">
                                        <?php echo ucfirst($user['status']); ?>
                                    </span>
                                    <span class="kyc-badge kyc-<?php echo $user['kyc_status']; ?>">
                                        <?php
                                        $kyc_labels = [
                                            'pending' => 'KYC Pending',
                                            'verified' => 'KYC Verified',
                                            'rejected' => 'KYC Rejected'
                                        ];
                                        echo $kyc_labels[$user['kyc_status']] ?? 'KYC Unknown';
                                        ?>
                                    </span>
                                </div>
                            </div>
                            <div class="account-avatar">
                                <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                            </div>
                        </div>

                        <div class="balance-display">
                            <div class="balance-main">
                                <p class="balance-label">Available Balance</p>
                                <h1 class="balance-amount"><?php echo formatCurrency($current_balance, $user['currency'] ?? 'USD'); ?></h1>
                                <p class="balance-details">
                                    <?php echo ucfirst($user['account_type']); ?> Account • <?php echo $user['currency'] ?? 'USD'; ?>
                                </p>
                            </div>
                            <div class="balance-actions">
                                <a href="../transfers/" class="action-btn primary">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                    </svg>
                                    Transfer Money
                                </a>
                                <a href="../cards/" class="action-btn secondary">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                                    </svg>
                                    Manage Cards
                                </a>
                                <a href="../statements/" class="action-btn secondary">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                    </svg>
                                    Statements
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Account Details Grid -->
                    <div class="account-details-grid">
                        <div class="detail-card">
                            <div class="detail-icon">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="detail-content">
                                <h4>Account Type</h4>
                                <p><?php echo ucfirst($user['account_type']); ?> Account</p>
                            </div>
                        </div>
                        <div class="detail-card">
                            <div class="detail-icon">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="detail-content">
                                <h4>KYC Status</h4>
                                <p><?php echo ucfirst($user['kyc_status']); ?></p>
                            </div>
                        </div>
                        <div class="detail-card">
                            <div class="detail-icon">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="detail-content">
                                <h4>Member Since</h4>
                                <p><?php echo date('M Y', strtotime($user['created_at'])); ?></p>
                            </div>
                        </div>
                        <div class="detail-card">
                            <div class="detail-icon">
                                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="detail-content">
                                <h4>Last Login</h4>
                                <p><?php echo $user['last_login'] ? date('M j, Y', strtotime($user['last_login'])) : 'First time'; ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <?php
                        // Get recent beneficiaries for quick actions
                        try {
                            $beneficiaries_sql = "SELECT * FROM beneficiaries WHERE user_id = ? ORDER BY created_at DESC LIMIT 6";
                            $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
                            $beneficiaries = [];
                            if ($beneficiaries_result) {
                                while ($row = $beneficiaries_result->fetch_assoc()) {
                                    $beneficiaries[] = $row;
                                }
                            }
                        } catch (Exception $e) {
                            $beneficiaries = [];
                        }

                        // Default quick actions if no beneficiaries
                        $default_actions = [
                            ['name' => 'Transfer', 'url' => '../transfers/', 'color' => '#f59e0b', 'amount' => $monthly_stats['total_sent'] ?? 0],
                            ['name' => 'Cards', 'url' => '../cards/', 'color' => '#8b5cf6', 'amount' => $monthly_stats['total_received'] ?? 0],
                            ['name' => 'Analytics', 'url' => '../dashboard/insights.php', 'color' => '#10b981', 'amount' => ($monthly_stats['total_sent'] ?? 0) + ($monthly_stats['total_received'] ?? 0)],
                            ['name' => 'Rewards', 'url' => '../dashboard/rewards.php', 'color' => '#ef4444', 'amount' => $monthly_stats['total_transactions'] ?? 0],
                            ['name' => 'Help', 'url' => '../dashboard/help.php', 'color' => '#6b7280', 'amount' => rand(1000, 5000)],
                            ['name' => 'Add New', 'url' => '../transfers/', 'color' => '#d1d5db', 'amount' => rand(100, 999)]
                        ];

                        $actions_to_show = !empty($beneficiaries) ? array_slice($beneficiaries, 0, 6) : $default_actions;
                        ?>

                        <?php foreach ($actions_to_show as $index => $action): ?>
                        <a href="<?php echo isset($action['url']) ? $action['url'] : '../transfers/'; ?>" class="quick-action">
                            <div class="quick-action-icon" style="background: <?php echo isset($action['color']) ? $action['color'] : '#6366f1'; ?>;">
                                <?php if (isset($action['name']) && !isset($action['account_number'])): ?>
                                    <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                        <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                                    </svg>
                                <?php else: ?>
                                    <?php echo strtoupper(substr($action['name'] ?? 'U', 0, 2)); ?>
                                <?php endif; ?>
                            </div>
                            <?php if (isset($action['account_number'])): ?>
                                <?php echo htmlspecialchars($action['name']); ?><br>
                                <small style="opacity: 0.8;"><?php echo htmlspecialchars($action['bank_name'] ?? 'Bank'); ?></small>
                            <?php else: ?>
                                <?php echo htmlspecialchars($action['name']); ?><br>
                                $<?php echo number_format($action['amount'] ?? 0, 0); ?>
                            <?php endif; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Monthly Financial Summary -->
                <div class="financial-summary-grid">
                    <div class="summary-card income">
                        <div class="summary-header">
                            <div class="summary-icon">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="summary-info">
                                <h4>Money In</h4>
                                <p>This Month</p>
                            </div>
                        </div>
                        <div class="summary-amount">
                            <?php echo formatCurrency($monthly_stats['total_received'] ?? 0, $user['currency'] ?? 'USD'); ?>
                        </div>
                        <div class="summary-change <?php echo $income_change >= 0 ? 'positive' : 'negative'; ?>">
                            <?php
                            if ($income_change != 0) {
                                echo ($income_change > 0 ? '+' : '') . number_format($income_change, 1) . '% ';
                                echo $income_change > 0 ? '↗' : '↘';
                            } else {
                                echo 'No change';
                            }
                            ?>
                        </div>
                        <div class="summary-details">
                            <?php echo number_format($monthly_stats['total_transactions'] ?? 0); ?> transactions
                        </div>
                    </div>

                    <div class="summary-card expense">
                        <div class="summary-header">
                            <div class="summary-icon">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293 7.707a1 1 0 011.414 0L9 9.414V17a1 1 0 11-2 0V9.414L5.707 10.707a1 1 0 01-1.414-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="summary-info">
                                <h4>Money Out</h4>
                                <p>This Month</p>
                            </div>
                        </div>
                        <div class="summary-amount">
                            <?php echo formatCurrency($monthly_stats['total_sent'] ?? 0, $user['currency'] ?? 'USD'); ?>
                        </div>
                        <div class="summary-change <?php echo $spend_change <= 0 ? 'positive' : 'negative'; ?>">
                            <?php
                            if ($spend_change != 0) {
                                echo ($spend_change > 0 ? '+' : '') . number_format($spend_change, 1) . '% ';
                                echo $spend_change > 0 ? '↗' : '↘';
                            } else {
                                echo 'No change';
                            }
                            ?>
                        </div>
                        <div class="summary-details">
                            Average: <?php echo formatCurrency($monthly_stats['avg_sent'] ?? 0, $user['currency'] ?? 'USD'); ?>
                        </div>
                    </div>

                    <div class="summary-card net">
                        <div class="summary-header">
                            <div class="summary-icon">
                                <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="summary-info">
                                <h4>Net Flow</h4>
                                <p>This Month</p>
                            </div>
                        </div>
                        <div class="summary-amount">
                            <?php
                            $net_flow = ($monthly_stats['total_received'] ?? 0) - ($monthly_stats['total_sent'] ?? 0);
                            echo formatCurrency($net_flow, $user['currency'] ?? 'USD');
                            ?>
                        </div>
                        <div class="summary-change <?php echo $net_flow >= 0 ? 'positive' : 'negative'; ?>">
                            <?php echo $net_flow >= 0 ? 'Positive Flow' : 'Negative Flow'; ?>
                        </div>
                        <div class="summary-details">
                            Balance Impact
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar Section -->
            <div class="sidebar-section">
                <!-- My Cards -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">My Cards</h3>
                        <a href="../cards/" style="color: #6366f1; font-size: 0.875rem; text-decoration: none;">Add New +</a>
                    </div>
                    <div style="padding: 1.5rem;">
                        <?php
                        // Get user's virtual cards
                        try {
                            $cards_sql = "SELECT * FROM virtual_cards WHERE user_id = ? AND status = 'active' LIMIT 1";
                            $cards_result = $db->query($cards_sql, [$user_id]);
                            $user_card = $cards_result ? $cards_result->fetch_assoc() : null;
                        } catch (Exception $e) {
                            $user_card = null;
                        }
                        ?>

                        <?php if ($user_card): ?>
                        <div class="virtual-card">
                            <div class="card-brand"><?php echo strtoupper($user_card['card_type']); ?></div>
                            <div style="margin-bottom: 1rem;">
                                <div style="font-size: 0.875rem; opacity: 0.8;"><?php echo htmlspecialchars($user_card['card_holder_name']); ?></div>
                            </div>
                            <div class="card-number"><?php echo htmlspecialchars($user_card['card_number']); ?></div>
                            <div class="card-details">
                                <div>
                                    <div style="font-size: 0.75rem; opacity: 0.8;">Exp</div>
                                    <div><?php echo sprintf('%02d/%02d', $user_card['expiry_month'], $user_card['expiry_year'] % 100); ?></div>
                                </div>
                                <div>
                                    <div style="font-size: 0.75rem; opacity: 0.8;">CVV</div>
                                    <div>***</div>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <div style="padding: 2rem; text-align: center; color: #6b7280;">
                            <p>No virtual cards found</p>
                            <a href="../cards/" style="color: #6366f1; text-decoration: none;">Create your first card</a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions Panel -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Quick Actions</h3>
                    </div>
                    <div style="padding: 1.5rem;">
                        <div class="quick-actions-panel">
                            <a href="../transfers/" class="quick-action-item">
                                <div class="quick-action-icon" style="background: #6366f1;">
                                    <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                        <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="action-title">Transfer Money</div>
                                    <div class="action-subtitle">Send to anyone</div>
                                </div>
                            </a>
                            <a href="../payments/" class="quick-action-item">
                                <div class="quick-action-icon" style="background: #10b981;">
                                    <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="action-title">Pay Bills</div>
                                    <div class="action-subtitle">Utilities & more</div>
                                </div>
                            </a>
                            <a href="../statements/" class="quick-action-item">
                                <div class="quick-action-icon" style="background: #8b5cf6;">
                                    <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="action-title">Statements</div>
                                    <div class="action-subtitle">Download & view</div>
                                </div>
                            </a>
                            <a href="../analytics/" class="quick-action-item">
                                <div class="quick-action-icon" style="background: #f59e0b;">
                                    <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="action-title">Analytics</div>
                                    <div class="action-subtitle">Spending insights</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Account Summary -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Account Summary</h3>
                    </div>
                    <div style="padding: 1.5rem;">
                        <div class="account-summary-items">
                            <div class="summary-item">
                                <div class="summary-label">Account Type</div>
                                <div class="summary-value"><?php echo ucfirst($user['account_type']); ?></div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-label">Status</div>
                                <div class="summary-value">
                                    <span class="status-badge status-<?php echo $user['status']; ?>">
                                        <?php echo ucfirst($user['status']); ?>
                                    </span>
                                </div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-label">KYC Status</div>
                                <div class="summary-value">
                                    <span class="kyc-badge kyc-<?php echo $user['kyc_status']; ?>">
                                        <?php echo ucfirst($user['kyc_status']); ?>
                                    </span>
                                </div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-label">Member Since</div>
                                <div class="summary-value"><?php echo date('M Y', strtotime($user['created_at'])); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Full-Width Transaction History -->
        <div class="transaction-history-section full-width">
                    <div class="section-header">
                        <div class="section-title">
                            <h3>Recent Transactions</h3>
                            <p>Your latest banking activity</p>
                        </div>
                        <div class="section-actions">
                            <a href="../transactions/" class="btn-secondary">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                </svg>
                                View All
                            </a>
                            <a href="../transactions/export.php" class="btn-outline">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                                Export
                            </a>
                        </div>
                    </div>

                    <div class="transaction-list-container">
                        <?php if ($recent_transactions && $recent_transactions->num_rows > 0): ?>
                            <div class="transaction-list">
                                <?php while ($transaction = $recent_transactions->fetch_assoc()): ?>
                                    <div class="enhanced-transaction-item" onclick="showTransactionDetails('<?php echo $transaction['transaction_id']; ?>')">
                                        <div class="transaction-main">
                                            <div class="transaction-avatar">
                                                <div class="avatar-icon <?php echo $transaction['direction'] === 'sent' ? 'sent' : 'received'; ?>">
                                                    <?php if ($transaction['direction'] === 'sent'): ?>
                                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                                                        </svg>
                                                    <?php else: ?>
                                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"/>
                                                        </svg>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="transaction-details">
                                                <div class="transaction-primary">
                                                    <h4><?php echo htmlspecialchars($transaction['other_party'] ?? 'Unknown'); ?></h4>
                                                    <span class="transaction-type"><?php echo ucfirst($transaction['direction']); ?></span>
                                                </div>
                                                <div class="transaction-secondary">
                                                    <span class="transaction-description"><?php echo htmlspecialchars($transaction['description'] ?? 'No description'); ?></span>
                                                    <span class="transaction-id">ID: <?php echo htmlspecialchars($transaction['transaction_id']); ?></span>
                                                </div>
                                            </div>
                                            <div class="transaction-amount-section">
                                                <div class="amount <?php echo $transaction['direction'] === 'sent' ? 'negative' : 'positive'; ?>">
                                                    <?php echo $transaction['direction'] === 'sent' ? '-' : '+'; ?><?php echo formatCurrency($transaction['amount'], $transaction['currency'] ?? 'USD'); ?>
                                                </div>
                                                <div class="transaction-time">
                                                    <?php echo date('M j, g:i A', strtotime($transaction['created_at'])); ?>
                                                </div>
                                                <div class="transaction-status">
                                                    <span class="status-badge status-<?php echo $transaction['status']; ?>">
                                                        <?php echo ucfirst($transaction['status']); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="transaction-expand-icon">
                                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>

                            <div class="transaction-footer">
                                <div class="transaction-summary">
                                    <span>Showing 5 most recent transactions</span>
                                </div>
                                <a href="../transactions/" class="view-all-link">
                                    View All Transactions
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="empty-transactions">
                                <div class="empty-icon">
                                    <svg width="64" height="64" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <h4>No transactions yet</h4>
                                <p>Your transaction history will appear here once you start banking with us.</p>
                                <div class="empty-actions">
                                    <a href="../transfers/" class="btn-primary">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                                        </svg>
                                        Send Money
                                    </a>
                                    <a href="../cards/" class="btn-secondary">
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                                        </svg>
                                        Get a Card
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>


</body>
</html>
